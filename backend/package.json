{"name": "ora-hume-backend", "version": "1.0.0", "description": "Scalable backend for Hume EVI integration with Google SSO", "main": "dist/server.js", "type": "module", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc && npm run build:fix", "build:fix": "if [ -d dist/backend/src ]; then find dist/backend/src -name '*.js' -exec sed -i.bak 's|../../../shared/|../shared/|g' {} + && cp -r dist/backend/src/* dist/ && find dist -name '*.js.bak' -delete; else echo 'TypeScript compiled directly to dist/'; fi", "start": "node dist/server.js", "db:migrate": "tsx src/database/migrate.ts", "db:seed": "tsx src/database/seed.ts"}, "dependencies": {"bcryptjs": "^2.4.3", "connect-pg-simple": "^9.0.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "google-auth-library": "^9.4.1", "helmet": "^7.1.0", "hume": "^0.12.1", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "pg": "^8.11.3", "uuid": "^9.0.1", "ws": "^8.18.2", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/connect-pg-simple": "^7.0.3", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-session": "^1.17.10", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/node": "^20.10.5", "@types/pg": "^8.10.9", "@types/uuid": "^9.0.7", "@types/ws": "^8.18.1", "tsx": "^4.19.4", "typescript": "^5.6.2"}, "engines": {"node": ">=18"}}