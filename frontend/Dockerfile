FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY frontend/package*.json ./

# Install dependencies
RUN npm ci

# Copy shared types
COPY shared ../shared

# Copy frontend source code
COPY frontend .

# Build the application
RUN npm run build

# Install serve to serve static files
RUN npm install -g serve

# Expose port (Cloud Run uses PORT env var)
EXPOSE $PORT

# Start the application
CMD serve -s dist -l $PORT
